<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\Golongan;
use App\Models\DokumenPeserta;
use App\Models\Pembayaran;
use App\Services\RegistrationNumberService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class PendaftaranController extends Controller
{
    /**
     * Display a listing of pendaftaran in admin daerah's wilayah
     */
    public function index(): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'pembayaran',
            'dokumenPeserta'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->orderBy('created_at', 'desc')
        ->paginate(20);

        // Get available golongan for filtering
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        return Inertia::render('AdminDaerah/Pendaftaran/Index', [
            'pendaftaran' => $pendaftaran,
            'golongan' => $golongan
        ]);
    }

    /**
     * Show the form for creating a new pendaftaran
     */
    public function create(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Get peserta from admin's wilayah
        $peserta = Peserta::with('user')
            ->where('id_wilayah', $adminWilayah)
            ->where('status_peserta', 'approved')
            ->orderBy('nama_lengkap')
            ->get();

        // Get available golongan
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        // If peserta ID is provided, get the specific peserta
        $selectedPeserta = null;
        if ($request->has('peserta')) {
            $selectedPeserta = Peserta::with('user')
                ->where('id_wilayah', $adminWilayah)
                ->findOrFail($request->peserta);
        }

        return Inertia::render('AdminDaerah/Pendaftaran/Create', [
            'peserta' => $peserta,
            'golongan' => $golongan,
            'selectedPeserta' => $selectedPeserta
        ]);
    }

    /**
     * Store a newly created pendaftaran
     */
    public function store(Request $request)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $validated = $request->validate([
            'id_peserta' => 'required|exists:peserta,id_peserta',
            'id_golongan' => 'required|exists:golongan,id_golongan',
            'nomor_urut' => 'nullable|integer|min:1',
            'documents' => 'nullable|array',
            'documents.*' => 'file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
        ]);

        // Verify peserta belongs to admin's wilayah
        $peserta = Peserta::where('id_peserta', $validated['id_peserta'])
            ->where('id_wilayah', $adminWilayah)
            ->firstOrFail();

        // Check if peserta already registered for this golongan
        $existingPendaftaran = Pendaftaran::where('id_peserta', $validated['id_peserta'])
            ->where('id_golongan', $validated['id_golongan'])
            ->first();

        if ($existingPendaftaran) {
            return back()->withErrors(['id_golongan' => 'Peserta sudah terdaftar pada golongan ini.']);
        }

        $golongan = Golongan::findOrFail($validated['id_golongan']);

        $pendaftaran = DB::transaction(function () use ($validated, $golongan, $request) {
            $tahun = date('Y');

            // Generate all required numbers
            try {
                $numbers = RegistrationNumberService::generateAllNumbers(
                    $golongan,
                    $tahun,
                    $validated['nomor_urut'] ?? null
                );
            } catch (\Exception $e) {
                throw new \Exception("Failed to generate registration numbers: " . $e->getMessage());
            }

            // Create pendaftaran with all required fields
            $pendaftaran = Pendaftaran::create([
                'id_peserta' => $validated['id_peserta'],
                'id_golongan' => $validated['id_golongan'],
                'nomor_pendaftaran' => $numbers['nomor_pendaftaran'],
                'nomor_peserta' => $numbers['nomor_peserta'],
                'nomor_urut' => $numbers['nomor_urut'],
                'tahun_pendaftaran' => $tahun,
                'status_pendaftaran' => 'submitted', // Admin daerah can directly submit
                'tanggal_daftar' => now(),
            ]);

            // Handle document uploads if any
            if ($request->has('documents') && is_array($request->file('documents'))) {
                foreach ($request->file('documents') as $jenisDocument => $file) {
                    if ($file && $file->isValid()) {
                        // Generate file name
                        $fileName = $this->generateFileName($file, $jenisDocument, $pendaftaran);
                        $filePath = $file->storeAs('dokumen-peserta', $fileName, 'public');

                        // Create document record
                        DokumenPeserta::create([
                            'id_pendaftaran' => $pendaftaran->id_pendaftaran,
                            'jenis_dokumen' => $jenisDocument,
                            'nama_file' => $fileName,
                            'path_file' => $filePath,
                            'ukuran_file' => $file->getSize(),
                            'mime_type' => $file->getMimeType(),
                            'status_verifikasi' => 'pending',
                            'uploaded_by' => Auth::id(),
                            'keterangan' => 'Diupload saat pendaftaran'
                        ]);
                    }
                }
            }

            // Create pembayaran record (commented for now)
            // Pembayaran::create([
            //     'id_pendaftaran' => $pendaftaran->id_pendaftaran,
            //     'jumlah_bayar' => $golongan->biaya_pendaftaran,
            //     'metode_pembayaran' => 'transfer',
            //     'status_pembayaran' => 'pending',
            //     'tanggal_bayar' => now(),
            // ]);

            return $pendaftaran;
        });

        return redirect()->route('admin-daerah.pendaftaran.show', $pendaftaran->id_pendaftaran)
            ->with('success', 'Pendaftaran berhasil dibuat. ' .
                (isset($validated['documents']) && count($validated['documents']) > 0
                    ? count($validated['documents']) . ' dokumen berhasil diupload.'
                    : 'Anda dapat mengupload dokumen di halaman detail pendaftaran.'));
    }

    /**
     * Display the specified pendaftaran
     */
    public function show(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'pembayaran',
            'dokumenPeserta'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        return Inertia::render('AdminDaerah/Pendaftaran/Show', [
            'pendaftaran' => $pendaftaran
        ]);
    }

    /**
     * Show the form for editing the specified pendaftaran
     */
    public function edit(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'golongan.cabangLomba'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        // Get available golongan
        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        return Inertia::render('AdminDaerah/Pendaftaran/Edit', [
            'pendaftaran' => $pendaftaran,
            'golongan' => $golongan
        ]);
    }

    /**
     * Update the specified pendaftaran
     */
    public function update(Request $request, string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        $validated = $request->validate([
            'id_golongan' => 'required|exists:golongan,id_golongan',
            'nomor_urut' => 'nullable|integer|min:1',
        ]);

        // Check if golongan changed and no conflict
        if ($pendaftaran->id_golongan != $validated['id_golongan']) {
            $existingPendaftaran = Pendaftaran::where('id_peserta', $pendaftaran->id_peserta)
                ->where('id_golongan', $validated['id_golongan'])
                ->where('id_pendaftaran', '!=', $id)
                ->first();

            if ($existingPendaftaran) {
                return back()->withErrors(['id_golongan' => 'Peserta sudah terdaftar pada golongan ini.']);
            }
        }

        $golongan = Golongan::findOrFail($validated['id_golongan']);

        // Validate nomor_urut if provided
        if (isset($validated['nomor_urut'])) {
            if (!RegistrationNumberService::validateNomorUrut($validated['nomor_urut'], $golongan)) {
                return back()->withErrors(['nomor_urut' => "Nomor urut harus antara {$golongan->nomor_urut_awal} dan {$golongan->nomor_urut_akhir}"]);
            }

            if (!RegistrationNumberService::isNomorUrutAvailable($validated['nomor_urut'], $golongan->id_golongan, $pendaftaran->id_pendaftaran)) {
                return back()->withErrors(['nomor_urut' => 'Nomor urut sudah digunakan']);
            }
        }

        $updateData = [
            'id_golongan' => $validated['id_golongan'],
        ];

        // If golongan changed, regenerate nomor_peserta
        if ($pendaftaran->id_golongan != $validated['id_golongan']) {
            $updateData['nomor_peserta'] = RegistrationNumberService::generateNomorPeserta($golongan, $pendaftaran->tahun_pendaftaran);
        }

        // Update nomor_urut if provided, otherwise keep existing
        if (isset($validated['nomor_urut'])) {
            $updateData['nomor_urut'] = $validated['nomor_urut'];
        }

        $pendaftaran->update($updateData);

        // Update pembayaran amount if golongan changed
        if ($pendaftaran->pembayaran && $pendaftaran->wasChanged('biaya_pendaftaran')) {
            $pendaftaran->pembayaran->update([
                'jumlah_bayar' => $golongan->biaya_pendaftaran
            ]);
        }

        return redirect()->route('admin-daerah.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil diperbarui.');
    }

    /**
     * Remove the specified pendaftaran
     */
    public function destroy(string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        // Only allow deletion if not yet approved
        if ($pendaftaran->status_pendaftaran === 'approved') {
            return back()->withErrors(['error' => 'Pendaftaran yang sudah disetujui tidak dapat dihapus.']);
        }

        $pendaftaran->delete();

        return redirect()->route('admin-daerah.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil dihapus.');
    }

    /**
     * Submit pendaftaran for approval
     */
    public function submit(string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        if ($pendaftaran->status_pendaftaran !== 'draft') {
            return back()->withErrors(['error' => 'Pendaftaran sudah disubmit atau disetujui.']);
        }

        $pendaftaran->update([
            'status_pendaftaran' => 'submitted',
            'tanggal_daftar' => now(),
        ]);

        return back()->with('success', 'Pendaftaran berhasil disubmit untuk persetujuan.');
    }

    /**
     * Get documents for pendaftaran
     */
    public function documents(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'dokumenPeserta.verifiedBy'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })
        ->findOrFail($id);

        // Define required documents
        $requiredDocuments = [
            'foto' => 'Foto Peserta',
            'ktp' => 'KTP/Identitas',
            'kartu_keluarga' => 'Kartu Keluarga',
            'surat_rekomendasi' => 'Surat Rekomendasi',
            'ijazah' => 'Ijazah Terakhir',
            'sertifikat' => 'Sertifikat Pendukung'
        ];

        return Inertia::render('AdminDaerah/Pendaftaran/Documents', [
            'pendaftaran' => $pendaftaran,
            'requiredDocuments' => $requiredDocuments
        ]);
    }

    /**
     * Upload document for pendaftaran
     */
    public function uploadDocument(Request $request, string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($id);

        $validated = $request->validate([
            'jenis_dokumen' => 'required|in:foto,ktp,kartu_keluarga,surat_rekomendasi,ijazah,sertifikat,lainnya',
            'file' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'keterangan' => 'nullable|string|max:255'
        ]);

        // Check if document already exists
        $existingDocument = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->where('jenis_dokumen', $validated['jenis_dokumen'])
            ->first();

        if ($existingDocument) {
            return back()->withErrors(['file' => 'Dokumen jenis ini sudah ada. Silakan hapus yang lama terlebih dahulu.']);
        }

        // Store file
        $file = $request->file('file');
        $fileName = $this->generateFileName($file, $validated['jenis_dokumen'], $pendaftaran);
        $filePath = $file->storeAs('dokumen-peserta', $fileName, 'public');

        // Create document record
        DokumenPeserta::create([
            'id_pendaftaran' => $pendaftaran->id_pendaftaran,
            'jenis_dokumen' => $validated['jenis_dokumen'],
            'nama_file' => $fileName,
            'path_file' => $filePath,
            'ukuran_file' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'status_verifikasi' => 'pending',
            'uploaded_by' => Auth::id(),
            'keterangan' => $validated['keterangan']
        ]);

        return back()->with('success', 'Dokumen berhasil diupload.');
    }

    /**
     * Delete document
     */
    public function deleteDocument(string $pendaftaranId, string $documentId)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $pendaftaran = Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->findOrFail($pendaftaranId);

        $dokumen = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->findOrFail($documentId);

        // Check if document can be deleted
        if ($dokumen->status_verifikasi === 'approved') {
            return back()->withErrors(['error' => 'Dokumen yang sudah diverifikasi tidak dapat dihapus.']);
        }

        // Delete file from storage
        if ($dokumen->path_file && Storage::disk('public')->exists($dokumen->path_file)) {
            Storage::disk('public')->delete($dokumen->path_file);
        }

        $dokumen->delete();

        return back()->with('success', 'Dokumen berhasil dihapus.');
    }

    /**
     * Get eligible golongan for a peserta
     */
    public function getEligibleGolongan(Request $request)
    {
        $validated = $request->validate([
            'id_peserta' => 'required|exists:peserta,id_peserta'
        ]);

        $adminWilayah = Auth::user()->id_wilayah;

        $peserta = Peserta::where('id_peserta', $validated['id_peserta'])
            ->where('id_wilayah', $adminWilayah)
            ->firstOrFail();

        $golongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->get()
            ->filter(function ($g) use ($peserta) {
                return $this->checkEligibility($peserta, $g);
            })
            ->values();

        return response()->json($golongan);
    }

    /**
     * Generate file name for document
     */
    private function generateFileName($file, $jenisDocument, $pendaftaran): string
    {
        $extension = $file->getClientOriginalExtension();
        return $jenisDocument . '_' . $pendaftaran->nomor_pendaftaran . '_' . time() . '.' . $extension;
    }

    /**
     * Check if peserta is eligible for golongan
     */
    private function checkEligibility(Peserta $peserta, Golongan $golongan): bool
    {
        // Check gender
        if ($peserta->jenis_kelamin !== $golongan->jenis_kelamin) {
            return false;
        }

        // Check age
        $age = $this->calculateAge($peserta->tanggal_lahir);
        if ($age < $golongan->batas_umur_min || $age > $golongan->batas_umur_max) {
            return false;
        }

        return true;
    }

    /**
     * Calculate age from birth date
     */
    private function calculateAge($birthDate): int
    {
        $today = new \DateTime();
        $birth = new \DateTime($birthDate);
        return $today->diff($birth)->y;
    }
}
