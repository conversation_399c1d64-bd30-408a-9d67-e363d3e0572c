<script setup lang="ts">
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type User } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';
import {
    BookOpen,
    Folder,
    LayoutGrid,
    Users,
    Settings,
    FileText,
    Trophy,
    UserCheck,
    BarChart3,
    MapPin,
    Building2,
    Gavel,
    Calendar,
    CreditCard,
    Upload,
    Star,
    GraduationCap,
    Award,
    ClipboardList,
    UserPlus,
    Download,
    FileCheck
} from 'lucide-vue-next';
import { computed } from 'vue';
import AppLogo from './AppLogo.vue';


const page = usePage();
const user = page.props.auth.user as User;

const mainNavItems = computed((): NavItem[] => {
    const baseItems: NavItem[] = [];

    // Check if user exists and has role
    if (!user || !user.role) {
        return baseItems;
    }

    // Super Admin & Admin navigation items
    if (['superadmin', 'admin'].includes(user.role)) {
        baseItems.push(
            {
                title: 'Dashboard',
                href: '/admin/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Manajemen User',
                href: '/admin/users',
                icon: UserCheck,
            },
            {
                title: 'Manajemen Wilayah',
                href: '/admin/wilayah',
                icon: MapPin,
            },
            {
                title: 'Cabang Lomba',
                href: '/admin/cabang-lomba',
                icon: Trophy,
            },
            {
                title: 'Golongan Lomba',
                href: '/admin/golongan',
                icon: Award,
            },
            {
                title: 'Mimbar',
                href: '/admin/mimbar',
                icon: Building2,
            },
            {
                title: 'Dewan Hakim',
                href: '/admin/dewan-hakim',
                icon: Gavel,
            },
            {
                title: 'Pelaksanaan MTQ',
                href: '/admin/pelaksanaan',
                icon: Calendar,
            },
            {
                title: 'Manajemen Peserta',
                href: '/admin/peserta',
                icon: Users,
            },
            {
                title: 'Pendaftaran Lomba',
                href: '/admin/pendaftaran',
                icon: FileText,
            },
            {
                title: 'Pembayaran',
                href: '/admin/pembayaran',
                icon: CreditCard,
            },
            {
                title: 'Verifikasi Dokumen',
                href: '/admin/dokumen-verifikasi',
                icon: FileCheck,
            },
            {
                title: 'Laporan',
                href: '/admin/laporan',
                icon: BarChart3,
            }
        );
    }

    // Admin Daerah navigation items
    if (user.role === 'admin_daerah') {
        baseItems.push(
            {
                title: 'Dashboard',
                href: '/admin-daerah/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Manajemen Peserta',
                href: '/admin-daerah/peserta',
                icon: Users,
            },
            {
                title: 'Daftar Langsung',
                href: '/admin-daerah/peserta/create',
                icon: UserPlus,
            },
            {
                title: 'Pendaftaran Lomba',
                href: '/admin-daerah/pendaftaran',
                icon: FileText,
            },
            {
                title: 'Laporan Daerah',
                href: '/admin-daerah/laporan',
                icon: BarChart3,
            }
        );
    }

    // Peserta navigation items
    if (user.role === 'peserta') {
        baseItems.push(
            {
                title: 'Dashboard',
                href: '/peserta/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Profil Saya',
                href: '/peserta/profile',
                icon: Users,
            },
            {
                title: 'Pendaftaran Lomba',
                href: '/peserta/pendaftaran',
                icon: FileText,
            },
            {
                title: 'Dokumen',
                href: '/peserta/dokumen',
                icon: Upload,
            },
            {
                title: 'Pembayaran',
                href: '/peserta/pembayaran',
                icon: CreditCard,
            }
        );
    }

    // Dewan Hakim navigation items
    if (user.role === 'dewan_hakim') {
        baseItems.push(
            {
                title: 'Dashboard',
                href: '/dewan-hakim/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Profil Hakim',
                href: '/dewan-hakim/profile',
                icon: Users,
            },
            {
                title: 'Sistem Penilaian',
                href: '/dewan-hakim/penilaian',
                icon: ClipboardList,
            }
        );
    }

    return baseItems;
});

const footerNavItems: NavItem[] = [
    {
        title: 'MTQ Lampung',
        href: 'https://mtqlampung.id',
        icon: Folder,
    },
    {
        title: 'Bantuan',
        href: '/bantuan',
        icon: BookOpen,
    },
];
</script>

<template>
    <div>
        <Sidebar collapsible="icon" variant="floating">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" as-child>
                            <Link :href="route('dashboard')">
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain :items="mainNavItems" />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter :items="footerNavItems" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
        <slot />
    </div>
</template>
